@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base colors */
  --dark-blue: #0a0a20;
  --darker-blue: #050518;
  --neon-pink: #ff2aff;
  --neon-blue: #2afdff;
  --neon-orange: #ff9e2a;

  /* Color with opacity variants */
  --neon-pink-10: rgba(255, 42, 255, 0.1);
  --neon-pink-20: rgba(255, 42, 255, 0.2);
  --neon-pink-30: rgba(255, 42, 255, 0.3);
  --neon-pink-50: rgba(255, 42, 255, 0.5);
  --neon-pink-70: rgba(255, 42, 255, 0.7);
  --neon-pink-90: rgba(255, 42, 255, 0.9);

  --neon-blue-10: rgba(42, 253, 255, 0.1);
  --neon-blue-20: rgba(42, 253, 255, 0.2);
  --neon-blue-30: rgba(42, 253, 255, 0.3);
  --neon-blue-50: rgba(42, 253, 255, 0.5);
  --neon-blue-70: rgba(42, 253, 255, 0.7);
  --neon-blue-90: rgba(42, 253, 255, 0.9);

  --neon-orange-10: rgba(255, 158, 42, 0.1);
  --neon-orange-20: rgba(255, 158, 42, 0.2);
  --neon-orange-30: rgba(255, 158, 42, 0.3);
  --neon-orange-50: rgba(255, 158, 42, 0.5);
  --neon-orange-70: rgba(255, 158, 42, 0.7);
  --neon-orange-90: rgba(255, 158, 42, 0.9);

  /* Dark blue opacity variants */
  --dark-blue-50: rgba(10, 10, 32, 0.5);
  --dark-blue-80: rgba(10, 10, 32, 0.8);
  --dark-blue-95: rgba(10, 10, 32, 0.95);

  /* Text opacity variants */
  --text-white-40: rgba(255, 255, 255, 0.4);
  --text-white-50: rgba(255, 255, 255, 0.5);
  --text-white-60: rgba(255, 255, 255, 0.6);
  --text-white-70: rgba(255, 255, 255, 0.7);
  --text-white-80: rgba(255, 255, 255, 0.8);
  --text-white-90: rgba(255, 255, 255, 0.9);

  /* Common values */
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.75rem;
  --shadow-sm: 0 0 5px;
  --shadow-lg: 0 0 15px;
  --scanline-count: 50;
}

@layer base {
  body {
    background-color: var(--dark-blue);
    color: white;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-vt323);
  }

  /* Neon text selection styling - Pink by default */
  ::selection {
    background-color: var(--neon-pink-30);
    color: white;
    text-shadow: 0 0 8px var(--neon-pink-90);
  }

  ::-moz-selection {
    background-color: var(--neon-pink-30);
    color: white;
    text-shadow: 0 0 8px var(--neon-pink-90);
  }
}

@layer components {
  /* Shadow utilities */
  .shadow-neon-pink {
    box-shadow: var(--shadow-sm) var(--neon-pink-70);
  }

  .shadow-neon-pink-lg {
    box-shadow: var(--shadow-lg) var(--neon-pink-50);
  }

  .shadow-neon-blue {
    box-shadow: var(--shadow-sm) var(--neon-blue-70);
  }

  .shadow-neon-blue-lg {
    box-shadow: var(--shadow-lg) var(--neon-blue-50);
  }

  .shadow-neon-orange {
    box-shadow: var(--shadow-sm) var(--neon-orange-70);
  }

  .shadow-neon-orange-lg {
    box-shadow: var(--shadow-lg) var(--neon-orange-50);
  }

  /* Neon text classes */
  .neon-text-pink {
    color: var(--neon-pink);
    text-shadow: var(--shadow-sm) var(--neon-pink-70);
  }

  .neon-text-blue {
    color: var(--neon-blue);
    text-shadow: var(--shadow-sm) var(--neon-blue-70);
  }

  .neon-text-orange {
    color: var(--neon-orange);
    text-shadow: var(--shadow-sm) var(--neon-orange-70);
  }

  /* Neon border classes - consolidated */
  .neon-border, .neon-border-pink {
    border-color: var(--neon-pink);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm) var(--neon-pink-70);
  }

  .neon-border-blue {
    border-color: var(--neon-blue);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm) var(--neon-blue-70);
  }

  .neon-border-orange {
    border-color: var(--neon-orange);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm) var(--neon-orange-70);
  }

  .grid-overlay {
    background-image: linear-gradient(to right, var(--neon-blue-10) 1px, transparent 1px),
      linear-gradient(to bottom, var(--neon-blue-10) 1px, transparent 1px);
    background-size: 40px 40px;
  }

  .section-container {
    max-width: 56rem; /* max-w-4xl */
    margin-left: auto;
    margin-right: auto;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
    position: relative;
    z-index: 10;
  }

  .section-title {
    font-size: 2.25rem; /* text-4xl */
    margin-bottom: 2rem;
    font-family: var(--font-vt323);
    color: var(--neon-pink);
    text-shadow: var(--shadow-sm) var(--neon-pink-70);
  }

  /* Consolidated media queries */
  @media (min-width: 768px) {
    .section-container {
      padding-left: 1rem;
      padding-right: 1rem;
      padding-top: 3rem;
      padding-bottom: 3rem;
    }

    .section-title {
      font-size: 3rem; /* md:text-5xl */
    }
  }

  .card {
    background-color: var(--dark-blue-50);
    backdrop-filter: blur(4px);
    border: 1px solid var(--neon-blue-30);
    border-radius: var(--border-radius-lg);
    padding: 0.5rem;
    transition: all 0.3s ease;
  }

  @media (min-width: 768px) {
    .card {
      padding: 1.5rem;
    }
  }

  .card:hover {
    border-color: var(--neon-pink-70);
    box-shadow: var(--shadow-lg) var(--neon-pink-30);
  }

  /* VHS Title Animation */
  .vhs-title {
    position: relative;
    overflow: hidden;
    display: inline-block;
  }

  .vhs-title::before,
  .vhs-title::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    pointer-events: none;
  }

  .vhs-title::before {
    height: 100%;
    background: repeating-linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.15),
      rgba(0, 0, 0, 0.15) 1px,
      transparent 1px,
      transparent 2px
    );
    z-index: 1;
    animation: scanlines 8s linear infinite;
  }

  .vhs-title::after {
    height: 3px;
    background-color: var(--text-white-50);
    box-shadow: var(--shadow-sm) var(--text-white-50);
    opacity: 0;
    z-index: 2;
    animation: tracking 10s linear infinite;
  }

  .vhs-title.glitching::after {
    opacity: 1;
  }

  @keyframes scanlines {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 0 calc(var(--scanline-count) * 2px);
    }
  }

  @keyframes tracking {
    0% {
      top: -10%;
      opacity: 0;
    }
    10% {
      top: 10%;
      opacity: 0.8;
    }
    11% {
      top: 10%;
      opacity: 0;
    }
    40% {
      top: 40%;
      opacity: 0;
    }
    41% {
      top: 40%;
      opacity: 0.8;
    }
    42% {
      top: 40%;
      opacity: 0;
    }
    70% {
      top: 70%;
      opacity: 0;
    }
    71% {
      top: 70%;
      opacity: 0.8;
    }
    72% {
      top: 70%;
      opacity: 0;
    }
    100% {
      top: 100%;
      opacity: 0;
    }
  }

  /* Audio Player Styles */

  /* Style the audio player dialog for mobile */
  [data-audio-player-dialog="true"] {
    width: 90vw !important;
    max-width: 350px !important;
    height: auto !important;
    max-height: 90vh !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    border-radius: var(--border-radius-lg) !important;
    margin: 0 !important;
    z-index: 9999 !important;
  }

  /* Hide the default close button */
  [data-audio-player-dialog="true"] > button[data-radix-dialog-close] {
    display: none !important;
  }

  /* Override the default dialog styles */
  [data-audio-player-dialog="true"] ~ [data-radix-dialog-overlay] {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }

  /* Tooltip styles - ensure tooltips display correctly on all devices */
  [data-radix-popper-content-wrapper] [data-radix-tooltip-content] {
    width: auto !important;
    height: auto !important;
    max-height: none !important;
    max-width: 300px !important;
    position: absolute !important;
    border-radius: var(--border-radius-lg) !important;
    overflow: visible !important;
    z-index: 9999 !important;
  }

  /* Specific styles for audio player tooltip */
  [data-radix-popper-content-wrapper] [data-audio-player-tooltip="true"] {
    width: auto !important;
    max-width: 250px !important;
    border-radius: var(--border-radius-lg) !important;
  }

  /* Base styles for all popovers */
  [data-radix-popper-content-wrapper] [data-radix-popover-content] {
    z-index: 9999 !important;
  }

  /* Force full-screen modal for popovers only (not tooltips) on mobile */
  @media (max-width: 1023px) {
    [data-radix-popper-content-wrapper] [data-radix-popover-content] {
      width: 100vw !important;
      height: 100vh !important;
      max-height: 100vh !important;
      max-width: 100vw !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      transform: none !important;
      position: fixed !important;
      border-radius: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
    }

    /* Make the popover content fill the screen */
    [data-radix-popper-content-wrapper] [data-radix-popover-content] > div {
      width: 100% !important;
      height: 100% !important;
      max-height: 100vh !important;
      max-width: 100vw !important;
      border-radius: 0 !important;
      display: flex !important;
      flex-direction: column !important;
      margin: 0 !important;
      padding: 0 !important;
    }

    /* Make the content area scrollable */
    [data-radix-popper-content-wrapper] [data-radix-popover-content] > div > div {
      flex: 1 !important;
      overflow-y: auto !important;
      width: 100% !important;
    }

    /* Special handling for experience popovers */
    [data-radix-popper-content-wrapper] [data-experience-popover="true"] {
      transform: none !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      position: fixed !important;
      width: 100vw !important;
      height: 100vh !important;
      max-width: 100vw !important;
      max-height: 100vh !important;
      border-radius: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
    }

    /* Ensure the wrapper doesn't apply transforms */
    [data-radix-popper-content-wrapper]:has([data-experience-popover="true"]) {
      transform: none !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      position: fixed !important;
    }
  }

  /* Responsive text for SB initials in profile */
  .text-responsive-sb {
    font-size: clamp(3.5rem, 15vw, 6rem); /* Base size */
    line-height: 1;
    text-align: center;
  }

  /* Responsive sizes handled with a single media query */
  @media (min-width: 768px) and (max-width: 1023px) {
    .text-responsive-sb {
      font-size: clamp(4rem, 18vw, 6rem);
    }
  }

  @media (min-width: 1024px) {
    .text-responsive-sb {
      font-size: clamp(4.5rem, 22vw, 6rem);
    }
  }
}
