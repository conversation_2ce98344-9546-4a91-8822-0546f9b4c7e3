# <PERSON>'s Portfolio

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge)](https://vercel.com/sean-blon<PERSON>-projects/portfolio)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.dev-black?style=for-the-badge)](https://v0.dev)

My personal portfolio site built with Next.js and React. Features a retro vaporwave/outrun aesthetic with some custom animations and effects.

## Tech Stack

- Next.js 15
- React 19
- TypeScript
- Radix UI for accessible components
- Tailwind CSS
- Framer Motion for animations
- ESLint
- Vercel Analytics & Speed Insights
- Deployed on Vercel

## Development

```sh
# Install dependencies
pnpm install

# Run development server
pnpm dev

# Run linter with auto-fix
pnpm lint
```

## Notes

The site uses a custom VHS-style glitch effect for the hero section and intersection observer for scroll animations. The design is inspired by 80s/90s vaporwave and outrun aesthetics.
